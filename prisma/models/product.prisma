model Collection {
  id   Int    @id @default(autoincrement()) @map("id")
  name String @map("name") @db.VarChar(255)
  tag  String @map("tag") @db.VarChar(255)

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  products Product[]

  @@index([name])
  @@map("collections")
}

model Product {
  id          Int    @id @default(autoincrement()) @map("id")
  productName String @map("product_name") @db.VarChar(255)

  /// [ProductMedia]
  media Json[]

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  variants        Variant[]
  shopifyProducts ShopifyProduct[]
  collection      Collection       @relation(fields: [collectionId], references: [id], onDelete: Cascade)
  collectionId    Int              @map("collection_id")

  @@map("products")
}

model Variant {
  id          Int    @id @default(autoincrement()) @map("id")
  sku         String @unique @map("sku") @db.VarChar(16)
  variantName String @map("variant_name") @db.VarChar(255)

  /// [VariantOptions]
  options Json?

  /// [VariantMedia]
  media Json[]

  searchVector Unsupported("tsvector")? @map("search_vector")

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  // Relations
  product   Product @relation(fields: [productId], references: [id], onDelete: Cascade)
  productId Int     @map("product_id")

  shopifyVariants ShopifyVariant[]

  @@index([sku])
  @@index(fields: [searchVector], name: "variants_search_vector_idx", type: Gin)
  @@map("variants")
}

model ProductFile {
  id        Int      @id @default(autoincrement()) @map("id")
  name      String   @map("name") @db.VarChar(255)
  s3Key     String   @map("s3_key") @db.VarChar(500)
  s3Url     String   @map("s3_url") @db.VarChar(1000)
  status    String   @map("status") @default("UPLOADED") @db.VarChar(50)

  // Timestamps
  createdAt DateTime @default(now()) @map("created_at")
  updatedAt DateTime @updatedAt @map("updated_at")

  @@map("product_files")
}