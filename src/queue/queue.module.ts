import { SQSClient } from "@aws-sdk/client-sqs";
import { Modu<PERSON> } from "@nestjs/common";
import { ConfigModule, ConfigType } from "@nestjs/config";
import awsConfig from "../config/configuration/aws.config";
import { SqsConsumerService } from "./sqs-consumer.service";

@Module({
  imports: [ConfigModule],
  providers: [
    {
      provide: SQSClient,
      inject: [awsConfig.KEY],
      useFactory: (config: ConfigType<typeof awsConfig>) => {
        const clientConfig: ConstructorParameters<typeof SQSClient>[0] = {
          region: config.region ?? "eu-central-1",
        };

        // Use explicit credentials only for local development
        // In ECS, let AWS SDK use IAM roles automatically
        if (
          config.accessKeyId !== undefined &&
          config.secretAccessKey !== undefined
        ) {
          clientConfig.credentials = {
            accessKeyId: config.accessKeyId,
            secretAccessKey: config.secretAccessKey,
          };
        }

        return new SQSClient(clientConfig);
      },
    },
    SqsConsumerService,
  ],
  exports: [SQSClient, SqsConsumerService],
})
export class QueueModule {}
