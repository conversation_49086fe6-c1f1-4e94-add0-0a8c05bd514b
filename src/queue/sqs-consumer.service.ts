import { Message, SQSClient } from "@aws-sdk/client-sqs";
import { Injectable, Logger } from "@nestjs/common";
import { Consumer } from "sqs-consumer";

export interface IConsumerConfig {
  queueUrl: string;
  handleMessage: (message: Message) => Promise<void>;
  messageAttributeNames?: string[];
  consumerName?: string;
}

@Injectable()
export class SqsConsumerService {
  private readonly logger = new Logger(SqsConsumerService.name);
  private consumers: Map<string, Consumer> = new Map();

  constructor(private readonly sqsClient: SQSClient) {}

  setupConsumer(
    handleMessage: (message: Message) => Promise<void>,
    messageAttributeNames: string[] = [],
    queueUrl: string,
  ) {
    return this.createConsumer({
      queueUrl,
      handleMessage,
      messageAttributeNames,
      consumerName: "default",
    });
  }

  createConsumer(config: IConsumerConfig): Consumer {
    const {
      queueUrl,
      handleMessage,
      messageAttributeNames = [],
      consumerName = "unnamed",
    } = config;

    if (this.consumers.has(consumerName)) {
      this.logger.warn(
        `Consumer ${consumerName} already exists. Stopping existing consumer.`,
      );
      this.stopConsumer(consumerName);
    }

    const consumer = Consumer.create({
      queueUrl,
      messageAttributeNames,
      handleMessage,
      sqs: this.sqsClient,
    });

    this.setupConsumerEvents(consumer, consumerName);

    consumer.start();
    this.consumers.set(consumerName, consumer);
    this.logger.log(
      `SQS Consumer '${consumerName}' started for queue: ${queueUrl}`,
    );

    return consumer;
  }

  private setupConsumerEvents(consumer: Consumer, consumerName: string) {
    consumer.on("error", (err) => {
      this.logger.error(
        `SQS Consumer '${consumerName}' error: ${err.message}`,
        err.stack,
      );
    });

    consumer.on("processing_error", (err) => {
      this.logger.error(
        `SQS Consumer '${consumerName}' processing error: ${err.message}`,
        err.stack,
      );
    });

    consumer.on("timeout_error", (err) => {
      this.logger.error(
        `SQS Consumer '${consumerName}' timeout error: ${err.message}`,
        err.stack,
      );
    });

    consumer.on("stopped", () => {
      this.logger.warn(`SQS Consumer '${consumerName}' stopped`);
    });

    consumer.on("message_received", () => {
      this.logger.debug(`SQS Consumer '${consumerName}' received message`);
    });

    consumer.on("message_processed", () => {
      this.logger.debug(`SQS Consumer '${consumerName}' processed message`);
    });
  }

  stopConsumer(consumerName?: string) {
    if (consumerName) {
      const consumer = this.consumers.get(consumerName);
      if (consumer) {
        consumer.stop();
        this.consumers.delete(consumerName);
        this.logger.log(`SQS Consumer '${consumerName}' stopped`);
      }
    } else {
      // Stop all consumers (backward compatibility)
      this.consumers.forEach((consumer, name) => {
        consumer.stop();
        this.logger.log(`SQS Consumer '${name}' stopped`);
      });
      this.consumers.clear();
    }
  }

  stopAllConsumers() {
    this.stopConsumer();
  }

  getConsumer(consumerName: string): Consumer | undefined {
    return this.consumers.get(consumerName);
  }

  getActiveConsumers(): string[] {
    return Array.from(this.consumers.keys());
  }
}
