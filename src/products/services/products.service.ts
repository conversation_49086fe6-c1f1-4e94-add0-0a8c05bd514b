import { Message } from "@aws-sdk/client-sqs";
import {
  Inject,
  Injectable,
  Logger,
  OnModuleDestroy,
  OnModuleInit,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import { PaginationInput } from "../../common/dto/pagination.input";
import { PaginationService } from "../../common/services/pagination.service";
import { assertIsString } from "../../common/types/type-guards";
import awsConfig from "../../config/configuration/aws.config";
import { SqsConsumerService } from "../../queue/sqs-consumer.service";
import { PaginatedProductsResponseDto } from "../dto/paginated-products-response.dto";
import { ProductWhereDto } from "../dto/product-where.dto";
import { ProductRepository } from "../repositories/product.repository";
import {
  assertIsProductDataMessage,
  IProductDataMessage,
} from "../types/message.types";

@Injectable()
export class ProductsService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(ProductsService.name);

  constructor(
    private readonly productRepository: ProductRepository,
    private readonly paginationService: PaginationService,
    private readonly sqsConsumerService: SqsConsumerService,
    @Inject(awsConfig.KEY)
    private readonly config: ConfigType<typeof awsConfig>,
  ) {}

  onModuleInit() {
    this.setupSqsConsumer();
  }

  onModuleDestroy() {
    this.sqsConsumerService.stopConsumer("products");
  }

  async findAll(whereInput: ProductWhereDto) {
    return this.productRepository.findAll(whereInput);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput: ProductWhereDto,
  ): Promise<PaginatedProductsResponseDto> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);
    const { totalCount, products: data } =
      await this.productRepository.findAllPaginated(
        paginationParams,
        whereInput,
      );

    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );

    return { meta, data };
  }

  findOne(id: number) {
    return this.productRepository.findOne(id);
  }

  remove(id: number) {
    return this.productRepository.remove(id);
  }

  private setupSqsConsumer() {
    const queueUrl = this.config.productsQueueUrl;
    if (!queueUrl) {
      this.logger.warn(
        "Products queue URL not configured. Skipping consumer setup.",
      );
      return;
    }

    this.sqsConsumerService.createConsumer({
      queueUrl,
      handleMessage: async (message: Message) => {
        await this.processMessage(message);
      },
      messageAttributeNames: ["action", "shopId"],
      consumerName: "products",
    });
  }

  private async processMessage(message: Message) {
    this.logger.log(`Processing message: ${message.MessageId}`);

    if (message.Body === undefined) {
      throw new Error("No body found in message");
    }
    const parsedBody: unknown = JSON.parse(message.Body);
    assertIsString(
      parsedBody,
      "Expected message body to contain a JSON string",
    );

    const messageAttributes = message.MessageAttributes;

    const shopId = messageAttributes?.shopId.StringValue;

    if (shopId === undefined) {
      this.logger.warn("No shop ID found in message attributes");
      return;
    }

    const productDataUnknown: unknown = JSON.parse(parsedBody);
    assertIsProductDataMessage(productDataUnknown);
    const productData: IProductDataMessage = productDataUnknown;

    await Promise.all([]);

    throw new Error("process message upsert not implemented");

    // const productInput: CreateProductDto = {
    //   id: BigInt(productData.id),
    //   shopId: BigInt(shopId),
    //   title: productData.title || "",
    //   variants: productData.variants.map((variant) => ({
    //     id: BigInt(variant.id),
    //     title: variant.title || undefined,
    //     displayName: variant.title || undefined, // Use title as displayName fallback
    //     price: parseFloat(String(variant.price)),
    //     compareAtPrice: parseFloat(String(variant.compare_at_price)),
    //     sku: variant.sku || undefined,
    //     productId: BigInt(productData.id), // Add productId
    //   })),
    // };
    //
    this.logger.log(`Upserting product: ${productData.id}`);
    // await this.productRepository.upsert(BigInt(productData.id), productInput);
    // this.logger.log(`Successfully processed product: ${productData.id}`);
  }
}
