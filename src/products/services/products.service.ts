import { Message } from "@aws-sdk/client-sqs";
import { Injectable, Logger } from "@nestjs/common";
import { SqsMessageHandler } from "@ssut/nestjs-sqs";
import { PaginationInput } from "../../common/dto/pagination.input";
import { PaginationService } from "../../common/services/pagination.service";
import { assertIsString } from "../../common/types/type-guards";
import { PaginatedProductsResponseDto } from "../dto/paginated-products-response.dto";
import { ProductWhereDto } from "../dto/product-where.dto";
import { ProductRepository } from "../repositories/product.repository";
import { assertIsProductDataMessage } from "../types/message.types";

@Injectable()
export class ProductsService {
  private readonly logger = new Logger(ProductsService.name);

  constructor(
    private readonly productRepository: ProductRepository,
    private readonly paginationService: PaginationService,
  ) {}

  async findAll(whereInput: ProductWhereDto) {
    return this.productRepository.findAll(whereInput);
  }

  async findAllPaginated(
    pagination: PaginationInput,
    whereInput: ProductWhereDto,
  ): Promise<PaginatedProductsResponseDto> {
    const paginationParams =
      this.paginationService.getPaginationParams(pagination);
    const { totalCount, products: data } =
      await this.productRepository.findAllPaginated(
        paginationParams,
        whereInput,
      );

    const meta = this.paginationService.getPaginationMeta(
      pagination,
      totalCount,
    );

    return { meta, data };
  }

  findOne(id: number) {
    return this.productRepository.findOne(id);
  }

  remove(id: number) {
    return this.productRepository.remove(id);
  }

  @SqsMessageHandler("products", false)
  async processMessage(message: Message) {
    this.logger.log(`Processing message: ${message.MessageId}`);

    if (message.Body === undefined) {
      throw new Error("No body found in message");
    }
    const parsedBody: unknown = JSON.parse(message.Body);
    assertIsString(
      parsedBody,
      "Expected message body to contain a JSON string",
    );

    const messageAttributes = message.MessageAttributes;

    const shopId = messageAttributes?.shopId.StringValue;

    if (shopId === undefined) {
      this.logger.warn("No shop ID found in message attributes");
      return;
    }

    const productDataUnknown: unknown = JSON.parse(parsedBody);
    assertIsProductDataMessage(productDataUnknown);

    await Promise.all([]);

    throw new Error("process message upsert not implemented");
  }
}
