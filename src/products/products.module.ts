import { Module } from "@nestjs/common";
import { CommonModule } from "../common/common.module";
import { QueueModule } from "../queue/queue.module";
import { VariantsModule } from "../variants/variants.module";
import { ProductsResolver } from "./products.resolver";
import { ProductRepository } from "./repositories/product.repository";
import { ProductsService } from "./services/products.service";

@Module({
  imports: [CommonModule, QueueModule, VariantsModule],
  providers: [ProductsResolver, ProductsService, ProductRepository],
  exports: [ProductsService],
})
export class ProductsModule {}
