import { Module } from "@nestjs/common";
import { CommonModule } from "../common/common.module";
import { AppSqsModule } from "../queue/sqs.module";
import { VariantsModule } from "../variants/variants.module";
import { ProductsResolver } from "./products.resolver";
import { ProductRepository } from "./repositories/product.repository";
import { ProductsService } from "./services/products.service";

@Module({
  imports: [CommonModule, AppSqsModule, VariantsModule],
  providers: [ProductsResolver, ProductsService, ProductRepository],
  exports: [ProductsService, ProductRepository],
})
export class ProductsModule {}
