import {
  isNullOrUndefined,
  isObject,
  isStringOrNumber,
  isTypedArray,
} from "../../common/types/type-guards";
import {
  isVariantMessage,
  IVariantMessage,
} from "../../variants/types/IVariantMessage";

export interface IProductDataMessage {
  id: string | number;
  title?: string | null;
  variants: IVariantMessage[];
}

export function isProductDataMessage(
  value: unknown,
): value is IProductDataMessage {
  if (!isObject(value)) {
    return false;
  }

  return (
    "id" in value &&
    isStringOrNumber(value.id) &&
    (!("title" in value) ||
      isNullOrUndefined(value.title) ||
      typeof value.title === "string") &&
    "variants" in value &&
    isTypedArray(value.variants, isVariantMessage)
  );
}

export function assertIsProductDataMessage(
  value: unknown,
): asserts value is IProductDataMessage {
  if (!isProductDataMessage(value)) {
    throw new Error(
      "Invalid product data structure: expected object with id, optional title, and variants array",
    );
  }
}
