import { Message } from "@aws-sdk/client-sqs";
import {
  Inject,
  Injectable,
  Logger,
  OnModuleDestroy,
  OnModuleInit,
} from "@nestjs/common";
import { ConfigType } from "@nestjs/config";
import awsConfig from "../../config/configuration/aws.config";
import { SqsConsumerService } from "../../queue/sqs-consumer.service";
import { ProductFileRepository } from "../repositories/product-file.repository";
import { ProductFileStatus } from "../types/product-file-status.types";
import { IProductFileMessage } from "./product-file.service";
import { S3Service } from "./s3.service";

@Injectable()
export class ProductFileConsumerService
  implements OnModuleInit, OnModuleDestroy
{
  private readonly logger = new Logger(ProductFileConsumerService.name);

  constructor(
    @Inject(awsConfig.KEY)
    private readonly config: ConfigType<typeof awsConfig>,
    private readonly sqsConsumerService: SqsConsumerService,
    private readonly fileRepository: ProductFileRepository,
    private readonly s3Service: S3Service,
  ) {}

  onModuleInit() {
    this.setupProductFileConsumer();
  }

  onModuleDestroy() {
    this.sqsConsumerService.stopConsumer("file-processing");
  }

  private setupProductFileConsumer() {
    const queueUrl = this.config.fileUploadQueueUrl;

    if (!queueUrl) {
      this.logger.warn(
        "File processing queue URL not configured. Skipping consumer setup.",
      );
      return;
    }

    try {
      this.sqsConsumerService.createConsumer({
        queueUrl,
        handleMessage: async (message: Message) => {
          await this.processFileMessage(message);
        },
        messageAttributeNames: ["action", "fileId"],
        consumerName: "file-processing",
      });

      this.logger.log("File processing consumer started successfully");
    } catch (error) {
      this.logger.error(`Failed to setup file processing consumer: ${error}`);
    }
  }

  private async processFileMessage(message: Message): Promise<void> {
    try {
      if (!message.Body) {
        this.logger.error("Received message without body");
        return;
      }

      const messageData: IProductFileMessage = JSON.parse(message.Body);
      this.logger.log(
        `${messageData.action} - Processing file message for file ID: ${messageData.fileId}`,
      );

      await this.fileRepository.updateStatus(
        messageData.fileId,
        ProductFileStatus.PROCESSING,
      );

      await this.processExcelFile(messageData);

      await this.fileRepository.updateStatus(
        messageData.fileId,
        ProductFileStatus.COMPLETED,
      );

      this.logger.log(
        `${messageData.action} - File processing completed for file ID: ${messageData.fileId}`,
      );
    } catch (error) {
      this.logger.error(`File processing failed: ${error}`);

      try {
        const messageData: IProductFileMessage = JSON.parse(
          message.Body || "{}",
        );
        if (messageData.fileId) {
          await this.fileRepository.updateStatus(
            messageData.fileId,
            ProductFileStatus.FAILED,
          );
        }
      } catch (updateError) {
        this.logger.error(
          `Failed to update file status to FAILED: ${updateError}`,
        );
      }

      throw error; // Re-throw to let SQS handle retry logic
    }
  }

  private async processExcelFile(
    messageData: IProductFileMessage,
  ): Promise<void> {
    try {
      this.logger.log(`Downloading file from S3: ${messageData.s3Key}`);

      // Download file from S3
      await this.s3Service.downloadFile(messageData.s3Key);

      // TODO: process file
    } catch (error) {
      this.logger.error(`Failed to process Excel file: ${error}`);
      throw error;
    }
  }
}
