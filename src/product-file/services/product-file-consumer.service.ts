import { Message } from "@aws-sdk/client-sqs";
import { Injectable, Logger } from "@nestjs/common";
import { SqsMessageHandler } from "@ssut/nestjs-sqs";
import { ProductRepository } from "../../products/repositories/product.repository";
import { ShopifyService } from "../../shopify/shopify.service";
import { VariantRepository } from "../../variants/repositories/variant.repository";
import { ExcelParserPipe } from "../pipes/excel-parser.pipe";
import { ProductFileRepository } from "../repositories/product-file.repository";
import { ProductFileStatus } from "../types/product-file-status.types";
import { IProductFileMessage } from "./product-file.service";
import { S3Service } from "./s3.service";

@Injectable()
export class ProductFileConsumerService {
  private readonly logger = new Logger(ProductFileConsumerService.name);

  constructor(
    private readonly fileRepository: ProductFileRepository,
    private readonly s3Service: S3Service,
    private readonly shopifyService: ShopifyService,
    private readonly productRepository: ProductRepository,
    private readonly variantRepository: VariantRepository,
    private readonly excelParserPipe: ExcelParserPipe,
  ) {}

  @SqsMessageHandler("product-file-upload", false)
  async processFileMessage(message: Message): Promise<void> {
    try {
      if (!message.Body) {
        this.logger.error("Received message without body");
        return;
      }

      const messageData: IProductFileMessage = JSON.parse(message.Body);
      this.logger.log(
        `${messageData.action} - Processing file message for file ID: ${messageData.fileId}`,
      );

      await this.fileRepository.updateStatus(
        messageData.fileId,
        ProductFileStatus.PROCESSING,
      );

      await this.processExcelFile(messageData);

      await this.fileRepository.updateStatus(
        messageData.fileId,
        ProductFileStatus.COMPLETED,
      );

      this.logger.log(
        `${messageData.action} - File processing completed for file ID: ${messageData.fileId}`,
      );
    } catch (error) {
      this.logger.error(`File processing failed: ${error}`);

      try {
        const messageData: IProductFileMessage = JSON.parse(
          message.Body || "{}",
        );
        if (messageData.fileId) {
          await this.fileRepository.updateStatus(
            messageData.fileId,
            ProductFileStatus.FAILED,
          );
        }
      } catch (updateError) {
        this.logger.error(
          `Failed to update file status to FAILED: ${updateError}`,
        );
      }

      throw error; // Re-throw to let SQS handle retry logic
    }
  }

  private async processExcelFile(
    messageData: IProductFileMessage,
  ): Promise<void> {
    try {
      this.logger.log(`Downloading file from S3: ${messageData.s3Key}`);

      const buffer = await this.s3Service.downloadFile(messageData.s3Key);

      const parsedProducts = new ExcelParserPipe().transform(buffer);
      this.logger.log(`Parsed ${parsedProducts.length} products from Excel`);

      const shopKey =
        process.env.SHOP_KEY || process.env.SHOPIFY_SHOP_KEY || "dev1";

      // For each product, send to Shopify and then store in DB

      for (const p of parsedProducts) {
        await this.handleProduct(shopKey, p.productName, p.variants, p.sizes);
      }
    } catch (error) {
      this.logger.error(`Failed to process Excel file: ${error}`);
      throw error;
    }
  }

  private async handleProduct(
    shopKey: string,
    productName: string,
    variants: { variantName: string; sku: string; size: string }[],
    sizes: string[],
  ): Promise<void> {
    await this.sendToShopify(shopKey, productName, variants, sizes);
    await this.persistToDb(productName, variants);
  }

  private async sendToShopify(
    shopKey: string,
    productName: string,
    variants: { variantName: string; sku: string; size: string }[],
    sizes: string[],
  ): Promise<void> {
    const productSet = {
      title: productName,
      productOptions: sizes.length
        ? [
            {
              name: "Size",
              position: 1,
              values: sizes.map((name) => ({ name })),
            },
          ]
        : undefined,
      variants: variants.map((v) => ({
        optionValues: [{ optionName: "Size", name: v.size }],
        sku: v.sku,
      })),
    };

    try {
      const result = await this.shopifyService.bulkSetProduct(
        shopKey,
        productSet,
        false,
      );
      this.logger.debug(`Shopify productSet result: ${JSON.stringify(result)}`);
    } catch (err) {
      this.logger.error(
        `Shopify productSet failed for ${productName}: ${String(err)}`,
      );
    }
  }

  private async persistToDb(
    productName: string,
    variants: { variantName: string; sku: string }[],
  ): Promise<void> {
    try {
      const product = await this.productRepository.upsertByName(productName);

      for (const v of variants) {
        await this.variantRepository.upsertByProductAndName({
          productId: product.id,
          variantName: v.variantName,
          sku: v.sku,
        });
      }
    } catch (dbErr) {
      this.logger.error(
        `DB persist failed for ${productName}: ${String(dbErr)}`,
      );
    }
  }
}
