import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { CommonModule } from "../common/common.module";
import awsConfig from "../config/configuration/aws.config";
import { QueueModule } from "../queue/queue.module";
import { ProductFileResolver } from "./product-file.resolver";
import { ProductFileRepository } from "./repositories/product-file.repository";
import { ProductFileConsumerService } from "./services/product-file-consumer.service";
import { ProductFileService } from "./services/product-file.service";
import { S3Service } from "./services/s3.service";

@Module({
  imports: [CommonModule, QueueModule, ConfigModule.forFeature(awsConfig)],
  providers: [
    ProductFileResolver,
    ProductFileService,
    ProductFileRepository,
    ProductFileConsumerService,
    S3Service,
  ],
  exports: [ProductFileService],
})
export class ProductFileModule {}
