import { Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { CommonModule } from "../common/common.module";
import awsConfig from "../config/configuration/aws.config";
import { ProductsModule } from "../products/products.module";
import { AppSqsModule } from "../queue/sqs.module";
import { ShopifyModule } from "../shopify/shopify.module";
import { VariantsModule } from "../variants/variants.module";
import { ExcelParserPipe } from "./pipes/excel-parser.pipe";
import { ProductFileResolver } from "./product-file.resolver";
import { ProductFileRepository } from "./repositories/product-file.repository";
import { ProductFileConsumerService } from "./services/product-file-consumer.service";
import { ProductFileService } from "./services/product-file.service";
import { S3Service } from "./services/s3.service";

@Module({
  imports: [
    CommonModule,
    AppSqsModule,
    ConfigModule.forFeature(awsConfig),
    ShopifyModule,
    ProductsModule,
    VariantsModule,
  ],
  providers: [
    ProductFileResolver,
    ProductFileService,
    ProductFileRepository,
    ProductFileConsumerService,
    S3Service,
    ExcelParserPipe,
  ],
  exports: [ProductFileService],
})
export class ProductFileModule {}
