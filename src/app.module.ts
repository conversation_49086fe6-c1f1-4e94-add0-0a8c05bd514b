import { join } from "path";
import {
  YogaF<PERSON>ration<PERSON><PERSON>,
  YogaFederationDriverConfig,
} from "@graphql-yoga/nestjs-federation";
import { Module } from "@nestjs/common";
import { ConfigModule, ConfigService, ConfigType } from "@nestjs/config";
import { GraphQLModule } from "@nestjs/graphql";
import {
  ShopifyAdminModule,
  ShopifyAdminConfigOptionsService,
} from "@sw-ecom360/shopify-admin-api";
import {
  BigIntResolver,
  DateTimeResolver,
  JSONResolver,
} from "graphql-scalars";
import { CollectionsModule } from "./collections/collections.module";
import { CommonModule } from "./common/common.module";
import appConfig from "./config/configuration/app.config";
import awsConfig from "./config/configuration/aws.config";
import databaseConfig from "./config/configuration/database.config";
import graphqlConfig from "./config/configuration/graphql.config";
import microserviceConfig from "./config/configuration/microservice.config";
import { validate as validateConfig } from "./config/validation";
import { HealthController } from "./health/health.controller";
import { InterserviceModule } from "./interservice/interservice.module";
import { ProductFileModule } from "./product-file/product-file.module";
import { ProductsModule } from "./products/products.module";
import { ShopifyProductsModule } from "./shopify-products/shopify-products.module";
import { ShopifyVariantsModule } from "./shopify-variants/shopify-variants.module";
import { VariantsModule } from "./variants/variants.module";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      cache: true,
      expandVariables: true,
      envFilePath:
        process.env.NODE_ENV === "production"
          ? undefined
          : [
              `.env.${process.env.NODE_ENV}.local`,
              `.env.${process.env.NODE_ENV}`,
              ".env.local",
              ".env",
            ],
      load: [
        appConfig,
        awsConfig,
        databaseConfig,
        graphqlConfig,
        microserviceConfig,
      ],
      validate: validateConfig,
      ignoreEnvFile: process.env.NODE_ENV === "production",
    }),
    GraphQLModule.forRootAsync<YogaFederationDriverConfig>({
      driver: YogaFederationDriver,
      inject: [appConfig.KEY, graphqlConfig.KEY],
      useFactory: (
        appConf: ConfigType<typeof appConfig>,
        graphqlConf: ConfigType<typeof graphqlConfig>,
      ) => ({
        path: `${appConf.globalPrefix}${graphqlConf.graphqlPath}`,
        typePaths: ["**/*.graphql"],
        definitions: {
          path: join(process.cwd(), "src/generated/graphql.ts"),
          outputAs: "class",
          defaultScalarType: "unknown",
          skipResolverArgs: false,
          customScalarTypeMapping: {
            BigInt: "bigint",
            DateTime: "Date",
            JSON: "Record<string, unknown>",
          },
        },
        resolverValidationOptions: {
          requireResolversForArgs: "error",
          requireResolversForNonScalar: "error",
          requireResolversForAllFields: "error",
          requireResolversForResolveType: "ignore",
          requireResolversToMatchSchema: "error",
        },
        inheritResolversFromInterfaces: true,
        resolvers: {
          BigInt: BigIntResolver,
          DateTime: DateTimeResolver,
          JSON: JSONResolver,
        },
        debug: process.env.NODE_ENV === "development",
        graphiql: process.env.NODE_ENV !== "production",
      }),
    }),
    ShopifyAdminModule.forRootAsync({
      imports: [ConfigModule],
      inject: [ConfigService],
      useClass: ShopifyAdminConfigOptionsService,
    }),
    CommonModule,
    ProductsModule,
    CollectionsModule,
    VariantsModule,
    ShopifyProductsModule,
    ShopifyVariantsModule,
    ProductFileModule,
    InterserviceModule,
  ],
  controllers: [HealthController],
  providers: [],
})
export class AppModule {}
