import { Injectable } from "@nestjs/common";
import { ShopifyAdminService } from "@sw-ecom360/shopify-admin-api";
import { GET_PRODUCTS } from "./operations/products.graphql";
import type { GetProductsQuery } from "../../generated/shopify/types/admin.generated";

@Injectable()
export class ShopifyService {
  constructor(private readonly shopifyAdmin: ShopifyAdminService) {}

  async getProducts(shopKey: string, limit: number = 50, after?: string) {
    return this.shopifyAdmin.request(shopKey, GET_PRODUCTS, {
      variables: {
        first: limit,
        after,
      },
    });
  }

  async getAllProductsFromShop(shopKey: string) {
    const allProducts: GetProductsQuery["products"]["edges"] = [];
    let hasNextPage = true;
    let cursor: string | null = null;

    while (hasNextPage) {
      const response = await this.getProducts(
        shopKey,
        250,
        cursor ?? undefined,
      );

      if (response.data?.products) {
        allProducts.push(...response.data.products.edges);
        hasNextPage = response.data.products.pageInfo.hasNextPage;
        cursor = response.data.products.pageInfo.endCursor ?? null;
      } else {
        break;
      }
    }

    return allProducts;
  }
}
