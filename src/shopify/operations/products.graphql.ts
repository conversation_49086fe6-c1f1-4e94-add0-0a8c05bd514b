export const GET_PRODUCTS = `#graphql
query GetProducts($first: Int!, $after: String) {
    products(first: $first, after: $after) {
        edges {
            node {
                id
                title
                handle
                variants(first: 100) {
                    edges {
                        node {
                            id
                            title
                            price
                            compareAtPrice
                            sku
                        }
                    }
                }
            }
        }
        pageInfo {
            hasNextPage
            endCursor
        }
    }
}
`;
